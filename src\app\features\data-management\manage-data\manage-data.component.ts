import {
  Component,
  EventEmitter,
  HostL<PERSON>ener,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Renderer2,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CellClickedEvent, GridApi, GridOptions } from 'ag-grid-community';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  combineLatest,
  debounceTime,
  filter,
  skipWhile,
  Subject,
  Subscription,
  take,
  takeUntil,
} from 'rxjs';

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  DATA_FILTERS_KEY_LABEL,
  PAGE_SIZE,
  SHOW_ENTRIES,
} from 'src/app/app.constants';
import {
  BHKType,
  DataDateType,
  DataFilterType,
  EnquiryType,
  FurnishStatus,
  Gender,
  MaritalStatusType,
  OfferType,
  PossessionType,
  Profession,
  PurposeType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  DataTopFilter,
  FilterPayloadKey,
} from 'src/app/core/interfaces/data-management.interface';
import { MasterAreaUnitType } from 'src/app/core/interfaces/master-data.interface';
import {
  changeCalendar,
  formatBudget,
  getAssignedToDetails,
  getBedsDisplay,
  getBHKDisplayString,
  getBRDisplayString,
  getIndexes,
  getISODateFormat,
  getLocationDetailsByObj,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  hexToRgba,
  isEmptyObject,
  onFilterChanged,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  AddCardData,
  ClearData,
  FetchAllData,
  FetchDataCommunicationByIds,
  FetchDataCustomStatusFilter,
  FetchDataCustomStatusFilterSuccess,
  FetchDataExcelUploadedList,
  FetchDataExportStatus,
  FetchDataSourceList,
  FetchDataStatus,
  FetchDataTopFilters,
  FetchDataTopFiltersSuccess,
  UpdateAllDataIsLoading,
  UpdateDataFilterPayload,
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  initialState as dmInitialFilterState,
  getAllData,
  getAllDataIsLoading,
  getDataCustomStatusFilter,
  getDataFilterCountHistory,
  getDataFiltersPayload,
  getDataSourceList,
  getDataStatusList,
  getDataTopFilters,
  getIsDataCustomStatusFilterLoading,
  getIsDataTopFiltersLoading,
  getTotalDataCount,
} from 'src/app/reducers/data/data-management.reducer';
import { FetchEmailSMTPListByUserId } from 'src/app/reducers/email/email-settings.action';
import { FetchFilter, FilterExist, SaveFilter, UpdateSavedFilter } from 'src/app/reducers/filter/filter.action';
import { getFilter, getFilterExist, getSavedFilter } from 'src/app/reducers/filter/filter.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchBulkOperation,
  FetchModuleWiseSearchProperties,
  FetchProjectList
} from 'src/app/reducers/lead/lead.actions';
import { getModuleWiseSearchProperties, getModuleWiseSearchPropertiesLoading } from 'src/app/reducers/lead/lead.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { getAreaUnits, getFetchModifiedDatesList, } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchRecentSearch,
  FetchReportees,
  FetchUsersListForReassignment,
  UpdateUserSearch
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getRecentSearch,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { ExportLeadsTrackerComponent } from 'src/app/shared/components/export-leads-tracker/export-leads-tracker.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment as env } from 'src/environments/environment';
import { ExcelUploadedStatusComponent } from '../../leads/excel-uploaded-status/excel-uploaded-status.component';
import { DataActionsComponent } from '../data-actions/data-actions.component';
import { ManageDataAdvanceFiltersComponent } from './manage-data-advance-filters/manage-data-advance-filters.component';
import { ManageDataCardComponent } from './manage-data-card/manage-data-card.component';

@Component({
  selector: 'manage-data',
  templateUrl: './manage-data.component.html',
})
export class ManageDataComponent implements OnInit, OnDestroy {
  @ViewChild(ManageDataCardComponent)
  manageDataCardComponent: ManageDataCardComponent;
  cleanCards$: Subscription | undefined;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canSearch: boolean = false;
  rowData: any[] = [];
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  dateTypeList: Array<string> = Object.keys(DataDateType).slice(9)?.filter(type => type !== 'Possession Date');
  dataFilterType = DataFilterType;
  dataFilterTypeCounts: any = {};
  _viewStates: Array<string> = Object.keys(DataFilterType).slice(9);
  canView: boolean = false;
  canViewAllData: boolean = false;
  selectedPageSize: number;
  filtersPayload: DataManagementFilters;
  archivedPayload: any;
  currArchiveOffset: number;
  currOffset: number = 0;
  searchTermSubject = new Subject<string>();
  gridOptions: GridOptions;
  gridApi: GridApi;
  dataTotalCount: number;
  currentPageNumber: number = 1;
  allUsers: any[] = [];
  deactiveUsers: any[] = [];
  defaultCurrency: string = '';
  activeAndInactiveUsers: any[] = [];
  searchTerm: string;
  dateType: string;
  filterDate: any;
  dataFiltersKeyLabel = DATA_FILTERS_KEY_LABEL;
  getPages = getPages;
  getAssignedToDetails = getAssignedToDetails;
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  getBedsDisplay = getBedsDisplay;
  getBRDisplayString = getBRDisplayString;
  getTimeZoneDate = getTimeZoneDate;
  statusList: any[] = [];
  sourceList: any[] = [];
  sourceIdMap: any = {};
  statusIdMap: any = {};
  recentSearches: any[] = [];
  selectedSearchFilters: any[] = [];
  showSearchDropdown: boolean = false;
  isModuleWiseSearchPropertiesLoading: boolean = false;
  searchFilters: any;
  allSearchFilters: any[];
  propertyType: Array<string> = JSON.parse(
    localStorage.getItem('propertyType')
  );

  areaSizeUnits: MasterAreaUnitType[] = [];
  columns: any[];
  defaultColumns: any[];
  gridColumnApi: any;
  moment = moment;
  canExport: boolean = false;
  globalSettingsData: any;
  showLeftNav: boolean = true;
  showWebUI: boolean = true;

  hexToRgba: Function = hexToRgba;
  topFilters: DataTopFilter[];
  selectedParentFilter: DataTopFilter | any;
  isLoadingCustomData: boolean = true;
  isLoadingTopFilters: boolean = true;
  selectedFirstLevelFilter: DataTopFilter | any;
  onFilterChanged = onFilterChanged;
  isGridDataLoading: boolean = true;
  cardData: any[] = [];
  isTopLevelFiltersLoading: boolean = true;
  cardDataPageNumber: number = 1;
  private resizeListener: () => void;
  private widthBelowThreshold: boolean = false;
  userData: any;
  s3BucketUrl: string = env.s3ImageBucketURL;
  canBulkUpload: boolean;
  canAdd: boolean;
  selectedDataTrackerOption: string;
  selectedAddDataOption: string;
  firstLevelFilter: any[] = [];
  secondLevelFilterList: any[];
  allTopFilters: any[];
  activeDataId: any;
  showCommunicationCount: boolean = false
  showFilterCount: boolean = false


  FileForm: FormGroup;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  canViewUnassigned: boolean;
  filteredTopFilters: DataTopFilter[];
  canBulkConvertToLead: boolean = false;
  canBulkEmail: boolean = false;
  isSavedFilterOpen: boolean = false;
  filters: any;
  selectedFilterId: string;
  columnMovedListener: any;
  selectedFilterName: any;
  doesFileNameExist: boolean = false;
  Gender = Gender;
  MaritalStatusType = MaritalStatusType;

  get isMobileView(): boolean {
    const width = window.innerWidth;
    if (width > 480) this.showWebUI = true;
    return width <= 480;
  }

  canBulkDelete: boolean = false;
  canBulkUpdateStatus: boolean = false;
  canBulkReassign: boolean = false;
  canBulkRestore: boolean = false;
  canBulkWhatsapp: boolean = false;
  selectedFilter: string = '';
  get selectedSecondFilter() {
    const { CustomFilterBaseIds, CustomFilterId } = this.filtersPayload;
    if (Array.isArray(CustomFilterBaseIds)) {
      return CustomFilterBaseIds.length === 2
        ? CustomFilterBaseIds[1]
        : CustomFilterBaseIds[0];
    }
    return CustomFilterId;
  }

  get showFilters(): boolean {
    return this.filtersPayload?.AssignTo?.length ||
      this.filtersPayload?.AssignedFromIds?.length ||
      this.filtersPayload?.CreatedByIds?.length ||
      this.filtersPayload?.LastModifiedByIds?.length ||
      this.filtersPayload?.ConvertedByIds?.length ||
      this.filtersPayload?.QualifiedByIds?.length ||
      this.filtersPayload?.EnquiryTypes?.length ||
      this.filtersPayload?.StatusIds?.length ||
      this.filtersPayload?.SourceIds?.length ||
      this.filtersPayload?.SubSources?.length ||
      this.filtersPayload?.Properties?.length ||
      this.filtersPayload?.PropertyType?.length ||
      this.filtersPayload?.PropertySubType?.length ||
      this.filtersPayload?.Projects?.length ||
      Number(this.filtersPayload?.ToMaxBudget) > 0 ||
      Number(this.filtersPayload?.ToMinBudget) > 0 ||
      Number(this.filtersPayload?.FromMaxBudget) > 0 ||
      Number(this.filtersPayload?.FromMinBudget) > 0 ||
      this.filtersPayload?.NoOfBHKs?.length ||
      this.filtersPayload?.BHKTypes?.length ||
      Number(this.filtersPayload?.MinCarpetArea) > 0 ||
      Number(this.filtersPayload?.MaxCarpetArea) > 0 ||
      Number(this.filtersPayload?.MinBuiltUpArea) > 0 ||
      Number(this.filtersPayload?.MaxBuiltUpArea) > 0 ||
      Number(this.filtersPayload?.MinSaleableArea) > 0 ||
      Number(this.filtersPayload?.MaxSaleableArea) > 0 ||
      Number(this.filtersPayload?.MinPropertyArea) > 0 ||
      Number(this.filtersPayload?.MaxPropertyArea) > 0 ||
      Number(this.filtersPayload?.MinNetArea) > 0 ||
      Number(this.filtersPayload?.MaxNetArea) > 0 ||
      this.filtersPayload?.AgencyNames?.length ||
      this.filtersPayload?.ChannelPartnerNames?.length ||
      this.filtersPayload?.CampaignNames?.length ||
      this.filtersPayload?.CompanyNames?.length ||
      this.filtersPayload?.Profession?.length ||
      this.filtersPayload?.DeletedByIds?.length ||
      this.filtersPayload?.RestoredByIds?.length ||
      this.filtersPayload?.Cities?.length ||
      this.filtersPayload?.States?.length ||
      this.filtersPayload?.Locations?.length ||
      this.filtersPayload?.Beds?.length ||
      this.filtersPayload?.Baths?.length ||
      this.filtersPayload?.Furnished?.length ||
      this.filtersPayload?.Floors?.length ||
      this.filtersPayload?.OfferTypes?.length ||
      this.filtersPayload?.Countries?.length ||
      this.filtersPayload?.PostalCodes?.length ||
      this.filtersPayload?.Localities?.length ||
      this.filtersPayload?.SubCommunities?.length ||
      this.filtersPayload?.Communities?.length ||
      this.filtersPayload?.TowerNames?.length ||
      Number(this.filtersPayload?.ReferralContactNo) ||
      this.filtersPayload?.ReferralName?.length ||
      this.filtersPayload?.ReferralEmail?.length ||
      this.filtersPayload?.SourcingManagers?.length ||
      this.filtersPayload?.ClosingManagers?.length ||
      this.filtersPayload?.Nationality?.length ||
      this.filtersPayload?.ClusterName?.length ||
      this.filtersPayload?.Purposes?.length ||
      this.filtersPayload?.UploadTypeName?.length ||
      this.filtersPayload?.UnitNames?.length ||
      (this.filtersPayload?.PossesionType) ? true : false ||
        this.filtersPayload?.DateOfBirth ||
        this.filtersPayload?.GenderTypes ||
        this.filtersPayload?.MaritalStatuses
        ||
        this.filtersPayload?.LandLine?.length
      ? true
      : false ||
        Number(this.filtersPayload?.CountryCode) ||
        Number(this.filtersPayload?.AltCountryCode) ||
        (this.filtersPayload?.PossesionType) ? true : false;
  }

  get isFilterSelected(): boolean {
    return (
      this.selectedFilter &&
      this.selectedFilter === JSON.stringify(this.filtersPayload || {})
    );
  }
  get isFilterUpdated(): boolean {
    return (
      this.selectedFilter &&
      this.selectedFilter !== JSON.stringify(this.filtersPayload || {})
    );
  }

  constructor(
    private gridOptionsService: GridOptionsService,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private _store: Store<AppState>,
    private router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    public activatedRoute: ActivatedRoute,
    private renderer: Renderer2,
    private ngZone: NgZone,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    public trackingService: TrackingService

  ) {
    this.headerTitle.setLangTitle('Manage Data');
    this.metaTitle.setTitle('CRM | Data');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowData = this.rowData;

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          this.globalSettingsData = data;
          this.defaultCurrency =
            data.countries && data.countries.length > 0
              ? data.countries[0].defaultCurrency
              : null;
        }
      });
  }

  ngOnInit(): void {
    this.showCommunicationCount = false
    this.showFilterCount = false
    if (this.filtersPayload) {
      this.filtersPayload.showCommunicationCount = false;
      this.filtersPayload.showFilterCount = false;
    }

    this.trackingService.trackFeature(`Web.Data.Page.Data.Visit`);

    this.resizeListener = this.renderer.listen(
      'window',
      'resize',
      this.onResize.bind(this)
    );
    this.FileForm = this.fb.group({
      name: [null, [Validators.required]],
    });

    // Call the function initially to check the initial window width
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this._store.dispatch(new FetchEmailSMTPListByUserId(userId));
    this._store.dispatch(new FetchReportees());
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getDataFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: DataManagementFilters) => {
        this.filtersPayload = {
          ...this.filtersPayload,
          ...filters,
          ProspectSearch: filters?.ProspectSearch || null,
          PageNumber: filters?.PageNumber || this.currentPageNumber,
          PageSize: filters?.PageSize || this.pageSize,
          DateType: filters?.DateType || 0,
          FromDate: filters?.FromDate,
          ToDate: filters?.ToDate,
          FilterType: filters?.FilterType || 0,
        };
        this.currOffset = filters?.PageNumber - 1;
        this.pageSize = filters?.PageSize || this.pageSize;
        this.selectedPageSize = this.pageSize;
        this.searchTerm = filters?.ProspectSearch || null;
        this.dateType = DataDateType[this.filtersPayload.DateType];
        this.defaultCurrency = filters?.Currency;
        this.filterDate = [
          patchTimeZoneDate(
            filters?.FromDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          patchTimeZoneDate(
            filters?.ToDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
        ];
      });

    combineLatest([
      this._store.select(getDataCustomStatusFilter),
      this._store.select(getDataTopFilters),
      this._store.select(getIsDataCustomStatusFilterLoading),
      this._store.select(getIsDataTopFiltersLoading),
    ]).subscribe(
      ([firstLevelFilter, topLevelFilter, isLoadingCustomData, isLoadingTop]: [
        any,
        any,
        any,
        any
      ]) => {
        this.firstLevelFilter = firstLevelFilter;

        this.activeDataId =
          this.firstLevelFilter?.find((fil: any) => fil?.isDefault === true)
            ?.id ??
          this.firstLevelFilter?.find((fil: any) => fil?.name === 'Active Data')
            ?.id;

        this.secondLevelFilterList = this.firstLevelFilter?.filter(
          (fil: any) => {
            if (
              Array.isArray(this.filtersPayload?.CustomFilterBaseIds) &&
              this.filtersPayload?.CustomFilterBaseIds?.length > 1
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds[1];
            } else if (
              Array.isArray(this.filtersPayload?.CustomFilterBaseIds) &&
              this.filtersPayload?.CustomFilterBaseIds?.length === 1
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds[0];
            } else if (
              typeof this.filtersPayload?.CustomFilterBaseIds === 'string'
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds;
            } else {
              return fil?.id === this.activeDataId;
            }
          }
        )[0]?.childType;
        this.topFilters = topLevelFilter?.dataTopLevelFilters?.filter(
          (fil: any) => !fil?.id
        );
        if (!this.selectedParentFilter) {
          this.selectedParentFilter =
            topLevelFilter?.dataTopLevelFilters?.find(
              (fil: any) => fil.name === 'AllData'
            ) ??
            topLevelFilter?.dataTopLevelFilters?.find(
              (fil: any) => fil?.displayName === 'All Data'
            );
        }
        this.isLoadingCustomData = isLoadingCustomData;
        this.isLoadingTopFilters = isLoadingTop;
        this.filteredTopFilters = this.topFilters?.filter(
          (filter) =>
            this.canViewUnassigned || filter.displayName !== 'Unassigned'
        );
      }
    );

    this.activatedRoute?.queryParamMap.pipe(take(1)).subscribe((data: any) => {
      if ('ChannelPartnerNames' in data?.params) {
        this.filtersPayload.ChannelPartnerNames = JSON.parse(
          data?.params?.ChannelPartnerNames
        );
      } else if ('CampaignNames' in data?.params) {
        this.filtersPayload.CampaignNames = JSON.parse(
          data?.params?.CampaignNames
        );
      } else if ('AgencyNames' in data?.params) {
        this.filtersPayload.AgencyNames = JSON.parse(data?.params?.AgencyNames);
      } else if ('Projects' in data?.params) {
        this.filtersPayload.Projects = [data?.params?.Projects];
      } else if ('Properties' in data?.params) {
        this.filtersPayload.Properties = [data?.params?.Properties];
      }
      if (data?.params?.SourceValue && data?.params?.isNavigatedFromSource) {
        const sourceValue = data?.params?.SourceValue;
        this._store.select(getDataSourceList)
          .pipe()
          .subscribe((sources: any) => {
            if (sources && sources.length) {
              const matchingSource = sources.find((source: any) =>
                String(source.value).trim().toLowerCase() === String(sourceValue).trim().toLowerCase());
              if (matchingSource) {
                this.filtersPayload.SourceIds = [matchingSource.id];
                this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));
                this.filterFunction();
              }
            }
          });
      }

      if (data?.params?.AssignTo) {
        this.filtersPayload.AssignTo = data?.params?.AssignTo
          ? data?.params?.AssignTo
          : [];
      }
      if (
        !Object.keys(data?.params)?.length &&
        data?.params?.leadReportGetData !== 'true'
      ) {
        return;
      }
      this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));

      const parsedFilters: any = JSON.parse(
        decodeURIComponent(data?.params?.filtersPayload)
      );
      this.filtersPayload = {
        ...this.filtersPayload,
        ...parsedFilters,
        DateType:
          DataDateType[parsedFilters.DateType as keyof typeof DataDateType],
        PageNumber: this.filtersPayload?.PageNumber || 1,
        PageSize: this.filtersPayload?.PageSize || this.pageSize,
      };
      if (parsedFilters.isNavigatedFromDashboard) {
        let allDataId = this.firstLevelFilter?.find(
          (fil: any) => fil?.name === 'All Data'
        )?.id;
        this.filtersPayload.CustomFilterId = allDataId;
        this.filtersPayload.CustomFilterBaseIds = allDataId;
      }
      this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));

      if (parsedFilters?.FromDate) {
        this.filtersPayload.FromDate = getISODateFormat(
          parsedFilters?.FromDate
        );
        this.filtersPayload.ToDate = getISODateFormat(parsedFilters?.ToDate);
      }
      this.onResize();
    });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        if (permissionsSet.has('Permissions.Prospects.Export')) {
          this.canExport = true;
        }
        if (permissionsSet.has('Permissions.Prospects.BulkUpload')) {
          this.canBulkUpload = true;
        }
        if (permissionsSet.has('Permissions.Prospects.Create')) {
          this.canAdd = true;
        }
        if (
          permissionsSet.has('Permissions.Prospects.ViewUnAssignedProspects')
        ) {
          this.canViewUnassigned = true;
        }
        if (permissionsSet.has('Permissions.Prospects.Search')) {
          this.canSearch = true;
        }
        this.canBulkUpdateStatus = permissionsSet.has('Permissions.Prospects.BulkUpdateStatus');
        this.canBulkReassign = permissionsSet.has('Permissions.Prospects.BulkReassign');
        this.canBulkDelete = permissionsSet.has('Permissions.Prospects.BulkDelete');
        this.canBulkRestore = permissionsSet.has('Permissions.Prospects.BulkRestore');
        this.canBulkWhatsapp = permissionsSet.has('Permissions.Prospects.BulkWhatsApp');
        this.canBulkEmail = permissionsSet.has('Permissions.Prospects.BulkEmail');
        this.canBulkConvertToLead = permissionsSet.has('Permissions.Prospects.BulkConvertToLead');
        if (permissionsSet.has('Permissions.Prospects.ViewAllProspects')) {
          this.canViewAllData = true;
        }

        if (permissionsSet.has('Permissions.Users.AssignToAny')) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
        this.initializeGridSettings();
      });
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchFilter());

    this._store
      .select(getFilter)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filters = data;
      });
    this._store
      .select(getSavedFilter)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.selectedFilterName = data?.data?.name
        this.selectedFilterId = data?.data?.id;
        this.FileForm.patchValue({
          name: data?.data?.name
        });
      }
      );
    this._store
      .select(getAllData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.length) {
          this.rowData = data || [];
          this.cardData = this.isMobileView
            ? [...this.cardData, ...this.rowData] || []
            : this.rowData || [];
        } else {
          this.rowData = data || [];
          this.cardData = [];
          this.cardDataPageNumber = 1;
        }
        this._store.dispatch(new AddCardData(this.cardData));
      });

    this._store
      .select(getTotalDataCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: any) => {
        this.dataTotalCount = count || 0;
      });

    this._store
      .select(getDataFilterCountHistory)
      .pipe(takeUntil(this.stopper))
      .subscribe((counts: any) => {
        let filterCounts: any = {};
        for (const key in counts) {
          filterCounts[key.toLocaleLowerCase()] = counts?.[key];
        }
        this.dataFilterTypeCounts = filterCounts;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.allUsers = users?.filter((user: any) => user?.isActive);
        this.deactiveUsers = users?.filter((user: any) => !user?.isActive);
        this.activeAndInactiveUsers = users;
      });
    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.filterFunction();
      });

    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.allUsers = users?.filter((user: any) => user?.isActive);
        this.deactiveUsers = users?.filter((user: any) => !user?.isActive);
        this.activeAndInactiveUsers = users;
      });

    this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.filterFunction();
      });

    this._store
      .select(getDataFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: DataManagementFilters) => {
        const isBudgetNotPresent = (!filters?.FromMaxBudget || !filters?.ToMaxBudget) && (!filters?.FromMinBudget || !filters?.ToMinBudget);
        const currency = isBudgetNotPresent ? null : filters?.Currency;
        this.filtersPayload = {
          ...this.filtersPayload,
          ...filters,
          ProspectSearch: filters?.ProspectSearch || null,
          PageNumber: filters?.PageNumber || this.currentPageNumber,
          PageSize: filters?.PageSize || this.pageSize,
          DateType: filters?.DateType || 0,
          FromDate: filters?.FromDate,
          ToDate: filters?.ToDate,
          FilterType: filters?.FilterType || 0,
          Currency: currency,
        };
        this.pageSize = filters?.PageSize || this.pageSize;
        this.selectedPageSize = this.pageSize;
        this.searchTerm = filters?.ProspectSearch || null;
        this.dateType = DataDateType[this.filtersPayload.DateType];
        this.filterDate = [filters?.FromDate, filters?.ToDate];
      });
    this._store.dispatch(new FetchModuleWiseSearchProperties(1));
    this._store.dispatch(new FetchRecentSearch('1'));

    const storedCustomStatusFilter = localStorage.getItem('dataLevelFilter');
    const storedTopFilters = localStorage.getItem('dataTopLevelFilters');
    const storedLastModified = localStorage.getItem('masterdatastatusLastModified');

    if (storedCustomStatusFilter && storedLastModified) {
      try {
        this._store
          .select(getFetchModifiedDatesList)
          .pipe(
            takeUntil(this.stopper),
            skipWhile((data: any) => !data || Object.keys(data).length === 0),
            take(1)
          )
          .subscribe((data: any) => {
            const apiLastModified = data?.CustomMasterDataStatus;

            let parsedStoredLastModified;
            try {
              parsedStoredLastModified = JSON.parse(storedLastModified);
            } catch (error) {
              parsedStoredLastModified = storedLastModified;
            }
            const storedDate = new Date(parsedStoredLastModified);
            const apiDate = new Date(apiLastModified);

            if (apiLastModified && apiDate.getTime() > storedDate.getTime()) {
              this._store.dispatch(new FetchDataCustomStatusFilter());
              localStorage.setItem('masterdatastatusLastModified', apiLastModified);
            } else {
              const parsedData = JSON.parse(storedCustomStatusFilter);
              this._store.dispatch(new FetchDataCustomStatusFilterSuccess(parsedData));
            }
          });
      } catch (error) {
        this._store.dispatch(new FetchDataCustomStatusFilter(this.filtersPayload));
      }
    } else {
      this._store.dispatch(new FetchDataCustomStatusFilter(this.filtersPayload));
    }

    if (storedTopFilters && storedLastModified) {
      try {
        this._store
          .select(getFetchModifiedDatesList)
          .pipe(
            takeUntil(this.stopper),
            skipWhile((data: any) => !data || Object.keys(data).length === 0),
            take(1)
          )
          .subscribe((data: any) => {
            const apiLastModified = data?.CustomMasterDataStatus;
            let parsedStoredLastModified;
            try {
              parsedStoredLastModified = JSON.parse(storedLastModified);
            } catch (error) {
              parsedStoredLastModified = storedLastModified;
            }
            const storedDate = new Date(parsedStoredLastModified);
            const apiDate = new Date(apiLastModified);

            if (apiLastModified && apiDate.getTime() > storedDate.getTime()) {
              this._store.dispatch(new FetchDataTopFilters());
              localStorage.setItem('masterdatastatusLastModified', apiLastModified);
            } else {
              const parsedData = JSON.parse(storedTopFilters);
              this._store.dispatch(new FetchDataTopFiltersSuccess(parsedData));
            }
          });
      } catch (error) {
        this._store.dispatch(new FetchDataTopFilters(this.filtersPayload));
      }
    } else {
      this._store.dispatch(new FetchDataTopFilters(this.filtersPayload));
    }


    this._store
      .select(getModuleWiseSearchPropertiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading) => (this.isModuleWiseSearchPropertiesLoading = isLoading));

    this._store
      .select(getModuleWiseSearchProperties)
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !data?.length)
      )
      .subscribe((data: any) => {
        this.searchFilters = data;
        this.allSearchFilters = [...data];
      });

    this._store.dispatch(new FetchDataStatus());
    this._store
      .select(getDataStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((statuses: any) => {
        this.statusList = statuses;
        statuses.forEach((status: any) => {
          this.statusIdMap[status.id] = status.displayName;
        });
      });


    this._store.dispatch(new FetchDataSourceList());
    this._store
      .select(getDataSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.sourceList = data;
        this.sourceList?.forEach((source: any) => {
          this.sourceIdMap[source.id] = source?.displayName;
        });
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getAllDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGridDataLoading = isLoading;
      });

    this.searchTermSubject.subscribe(() => {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
        ProspectSearch: this.searchTerm,
      };
      this.filterFunction();
    });
    this.shareDataService.showLeftNav$.subscribe((show: boolean) => {
      this.showLeftNav = show;
    });
    let isFirstChange = true;
    this.FileForm
      .get('name')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (isFirstChange) {
          isFirstChange = false;
          return;
        }

        if (
          value &&
          this.FileForm.controls.name.status === 'VALID'
        ) {
          this.doesFileNameExists();
        }
      });
    if (!localStorage.getItem('masterdatastatusLastModified')) {
      this._store.select(getFetchModifiedDatesList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (!isEmptyObject(data)) {
            localStorage.setItem('masterdatastatusLastModified', JSON.stringify(data?.CustomMasterDataStatus));
          }
        });
    }
    this._store.select(getDataCustomStatusFilter)
      .pipe(
        filter((data: any) => !!data && (Array.isArray(data) ? data.length > 0 : Object.keys(data).length > 0)),
        take(1)
      )
      .subscribe((data: any) => {
        localStorage.setItem('dataLevelFilter', JSON.stringify(data));
      });
    this._store.select(getDataTopFilters)
      .pipe(
        filter((data: any) =>
          !!data &&
          (
            (data.dataTopLevelFilters && data.dataTopLevelFilters.length > 0)
          )
        ),
        take(1)
      )
      .subscribe((data: any) => {
        if (data.dataTopLevelFilters && data.dataTopLevelFilters.length > 0) {
          localStorage.setItem('dataTopLevelFilters', JSON.stringify(data.dataTopLevelFilters));
        }
      });
  }

  exportDataReport() {
    const newPayload = {
      ...this.filtersPayload,
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
    };
    newPayload.CustomFilterBaseIds = Array.isArray(
      newPayload.CustomFilterBaseIds
    )
      ? [...newPayload.CustomFilterBaseIds]
      : [newPayload.CustomFilterBaseIds];
    const initialState: any = {
      payload: newPayload,
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(ExportMailComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
      initialState,
    });
  }

  getProfession(value: number) {
    return Profession[value];
  }

  toggleUI() {
    this.showWebUI = !this.showWebUI;
  }

  getUserName(id: string) {
    let userName = '';
    this.activeAndInactiveUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.firstName} ${user.lastName}`;
    });
    return userName;
  }

  getPropertyTypeName(id: string) {
    let propertyType = '';
    this.propertyType?.forEach((type: any) => {
      if (type.id === id) propertyType = type.displayName;
    });
    return propertyType;
  }

  getEnquiryTypeName(id: any) {
    return EnquiryType[id];
  }

  getPropertySubTypeName(id: string) {
    let propertySubType = '';
    let subTypeList: any[] = [];
    this.propertyType?.forEach((type: any) => {
      subTypeList.push(...type.childTypes);
    });
    subTypeList?.forEach((type: any) => {
      if (type.id === id) propertySubType = type.displayName;
    });
    return propertySubType;
  }

  getArea(area: number, id: string) {
    return area + ' ' + (this.getAreaUnit(id) || '');
  }

  getAreaUnit(id: string) {
    let areaUnit = '';
    this.areaSizeUnits?.forEach((type: any) => {
      if (type.id === id) areaUnit = type.unit;
    });
    return areaUnit;
  }

  getPossessionTypeDisplay(value: any): string {
    if (!value) return '';
    const numValue = Number(value);
    if (isNaN(numValue) || PossessionType[numValue] === undefined) return '';
    return PossessionType[numValue];
  }

  getArrayOfFilters(key: string, values: string) {
    if (key === 'PossesionType' && values) {
      const numValue = Number(values);
      if (!isNaN(numValue) && PossessionType[numValue] !== undefined) {
        return [numValue.toString()];
      }
      return [];
    }

    if ((key === 'FromPossesionDate' || key === 'ToPossesionDate') && this.filtersPayload?.PossesionType === 5 && values) {
      return [values];
    }

    if (key === 'FromPossesionDate' || key === 'ToPossesionDate') {
      return [];
    }

    const allowedKeys = [
      'SubSources',
      'Properties',
      'Projects',
      'Locations',
      'Nationality',
      'Cities',
      'AgencyNames',
      'States',
      'Countries',
      'PostalCodes',
      'Localities',
      'SubCommunities',
      'Communities',
      'TowerNames',
      'ChannelPartnerNames',
      'CampaignNames',
      'UnitNames',
      'ClusterName',
    ];
    if (
      key === 'DateType' ||
      key === 'FilterType' ||
      key === 'PageSize' ||
      key === 'PageNumber' ||
      key === 'FromDate' ||
      key === 'ToDate' ||
      key === 'Currency' ||
      key === 'CarpetAreaUnitId' ||
      key === 'BuiltUpAreaUnitId' ||
      key === 'SaleableAreaUnitId' ||
      key === 'PropertyAreaUnitId' ||
      key === 'NetAreaUnitId' ||
      key === 'ProspectSearch' ||
      key === 'path' ||
      key === 'IsWithTeam' ||
      key == 'CanAccessAllProspects' ||
      key === 'isNavigatedFromDashboard' ||
      key === FilterPayloadKey['ProspectVisiblity'] ||
      key === FilterPayloadKey['FirstLevelFilter'] ||
      key === FilterPayloadKey['SecondLevelFilter'] ||
      key == 'showFilterCount' ||
      key == 'showCommunicationCount' ||
      values?.length === 0 ||
      ((key === 'FromMinBudget' || key === 'ToMinBudget' || key === 'FromMaxBudget' || key === 'ToMaxBudget') && Number(values) === 0) ||
      ['CustomFilterId', 'CustomFilterBaseIds'].includes(key)
    )
      return [];
    else if (allowedKeys.includes(key)) {
      return values;
    }
    else if (
      (key === 'FromMinBudget' || key === 'ToMinBudget' || key === 'FromMaxBudget' || key === 'ToMaxBudget' || key === 'Currency') &&
      values
    )
      return [' ' + (this.filtersPayload?.Currency || this.defaultCurrency) + ' ' + values];
    return values?.toString()?.split(',');
  }

  getBHKNo(data: any) {
    return this.globalSettingsData?.isCustomLeadFormEnabled
      ? data
      : data?.map((bhk: any) => getBHKDisplayString(bhk))?.join(', ');
  }

  parentFilterChanged(filter: DataTopFilter) {
    if (filter?.enumValue === this.filtersPayload?.ProspectVisiblity) return;
    this.selectedParentFilter = filter;
    const isBudgetNotPresent =
      (!this.filtersPayload?.FromMaxBudget && !this.filtersPayload?.ToMaxBudget) &&
      (!this.filtersPayload?.FromMinBudget && !this.filtersPayload?.ToMinBudget);
    const currency = isBudgetNotPresent ? null : this.filtersPayload.Currency || this.defaultCurrency;

    this.selectedFirstLevelFilter = filter?.children?.filter(
      (firstLevelFilter: DataTopFilter) => {
        return firstLevelFilter?.isDefault;
      }
    )?.[0];
    this.currOffset = 0;
    this.filtersPayload = {
      ...this.filtersPayload,
      ProspectVisiblity: filter?.enumValue,
      FirstLevelFilter: this.selectedFirstLevelFilter?.enumValue,
      SecondLevelFilter: 0,
      PageNumber: 1,
      Currency: currency,
    };
    this.cardData = [];

    this.trackingService.trackFeature(
      `Web.Data.Menu.${filter?.displayName?.replace(/\s+/g, '')}.Click`
    );
    this.filterFunction();
  }

  firstLevelFilterChanged(filterEnum: any) {
    this.currOffset = 0;
    this.filtersPayload = {
      ...this.filtersPayload,
      CustomFilterId: filterEnum,
      CustomFilterBaseIds: filterEnum,
      SecondLevelFilter: 0,
      PageNumber: 1,
    };
    const selectedFilter = this.firstLevelFilter.find(filter => filter?.id === filterEnum);
    if (selectedFilter) {
      this.trackerFirstLevelFilter(selectedFilter?.name);
    }
    this.filterFunction();
  }

  secondLevelFilterChanged(
    filter: any,
    canRemoveSecondLevelFilter: boolean = false
  ) {
    if (
      filter?.id === this.filtersPayload?.CustomFilterId &&
      !canRemoveSecondLevelFilter
    )
      return;
    let secondLevelId = null;
    if (Array.isArray(this.filtersPayload?.CustomFilterBaseIds)) {
      secondLevelId = this.firstLevelFilter?.find(
        (fil: any) =>
          fil?.id === this.filtersPayload?.CustomFilterBaseIds[1] ||
          fil?.id === this.filtersPayload?.CustomFilterBaseIds[0]
      );
    } else if (typeof this.filtersPayload?.CustomFilterBaseIds === 'string') {
      secondLevelId = this.firstLevelFilter?.find(
        (fil: any) => fil?.id === this.filtersPayload?.CustomFilterBaseIds
      );
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      CustomFilterBaseIds: [filter?.id, secondLevelId?.id].filter(Boolean),
      CustomFilterId: filter?.id,
      PageNumber: 1,
    };
    if (canRemoveSecondLevelFilter) {
      this.filtersPayload = {
        ...this.filtersPayload,
        CustomFilterBaseIds: [secondLevelId?.id].filter(Boolean),
        CustomFilterId: secondLevelId?.id,
        PageNumber: 1,
      };
    }
    this.trackingService.trackFeature(
      `Web.Data.MenuLevel2.${filter?.name?.replace(
        /\s+/g,
        ''
      )}.Click`
    );
    this.filterFunction();
  }

  onRemoveFilter(key: string, value: string) {
    if (['PossesionType', 'FromPossesionDate', 'ToPossesionDate'].includes(key)) {
      this.filtersPayload = {
        ...this.filtersPayload,
        PossesionType: null,
        FromPossesionDate: null,
        ToPossesionDate: null,
      };
      this.filterFunction();
      return;
    }

    if (key === 'FromMinBudget' || key === 'ToMinBudget') {
      this.filtersPayload = {
        ...this.filtersPayload,
        FromMinBudget: null,
        ToMinBudget: null,
      };

      const maxBudgetCleared = !this.filtersPayload.FromMaxBudget && !this.filtersPayload.ToMaxBudget;
      if (maxBudgetCleared) {
        this.filtersPayload.Currency = null;
      }
    }

    else if (key === 'FromMaxBudget' || key === 'ToMaxBudget') {
      this.filtersPayload = {
        ...this.filtersPayload,
        FromMaxBudget: null,
        ToMaxBudget: null,
      };

      const minBudgetCleared = !this.filtersPayload.FromMinBudget && !this.filtersPayload.ToMinBudget;
      if (minBudgetCleared) {
        this.filtersPayload.Currency = null;
      }
    }
    else if (key === 'MaxCarpetArea' || key === 'MinCarpetArea') {
      this.filtersPayload = {
        ...this.filtersPayload,
        MinCarpetArea: null,
        MaxCarpetArea: null,
        CarpetAreaUnitId: null,
      };
    }
    else if (key === 'MinSaleableArea' || key === 'MaxSaleableArea') {
      this.filtersPayload = {
        ...this.filtersPayload,
        MinSaleableArea: null,
        MaxSaleableArea: null,
        SaleableAreaUnitId: null,
      };
    }
    else if (key === 'MinBuiltUpArea' || key === 'MaxBuiltUpArea') {
      this.filtersPayload = {
        ...this.filtersPayload,
        MinBuiltUpArea: null,
        MaxBuiltUpArea: null,
        BuiltUpAreaUnitId: null,
      };
    }
    else if (key === 'MaxPropertyArea' || key === 'MinPropertyArea') {
      this.filtersPayload = {
        ...this.filtersPayload,
        MinPropertyArea: null,
        MaxPropertyArea: null,
        PropertyAreaUnitId: null,
      };
    }
    else if (key === 'MinNetArea' || key === 'MaxNetArea') {
      this.filtersPayload = {
        ...this.filtersPayload,
        MinNetArea: null,
        MaxNetArea: null,
        NetAreaUnitId: null,
      };
    }
    else if (key === 'Profession') {
      const filteredValue: any = (
        this.filtersPayload?.Profession as number[]
      ).filter((item: any) => +item !== +value);
      this.filtersPayload = {
        ...this.filtersPayload,
        Profession: filteredValue,
      };
    } else if (
      [
        'AssignTo',
        'AssignedFromIds',
        'CreatedByIds',
        'LastModifiedByIds',
        'ConvertedByIds',
        'QualifiedByIds',
        'SourceIds',
        'StatusIds',
        'SubSources',
        'Properties',
        'PropertyType',
        'PropertySubType',
        'Projects',
        'EnquiryTypes',
        'NoOfBHKs',
        'BHKTypes',
        'AgencyNames',
        'ChannelPartnerNames',
        'CampaignNames',
        'DeletedByIds',
        'RestoredByIds',
        'Locations',
        'Nationality',
        'ClusterName',
        'Purposes',
        'UnitNames',
        'Cities',
        'States',
        'Countries',
        'PostalCodes',
        'Localities',
        'SubCommunities',
        'Communities',
        'TowerNames',
        'ClosingManagers',
        'SourcingManagers',
        'Beds',
        'Baths',
        'Furnished',
        'Floors',
        'OfferTypes',
      ].includes(key)
    ) {
      const targetKey = key as keyof DataManagementFilters;
      if (this.filtersPayload.hasOwnProperty(targetKey)) {
        const filteredValue: any = (
          this.filtersPayload[targetKey] as string[]
        ).filter((item: any) => {
          const matchValue = typeof item === 'number' ? Number(value) : value;
          return item !== matchValue;
        });
        this.filtersPayload = {
          ...this.filtersPayload,
          [targetKey]: filteredValue,
        };
      }
    } else {
      this.filtersPayload = {
        ...this.filtersPayload,
        [key]: null,
      };
    }

    // if (key !== 'MaxBudget' && key !== 'MinBudget') {
    //   this.filtersPayload = {
    //     ...this.filtersPayload,
    //     Currency: null,
    //   };
    // }

    this.filterFunction();
  }

  onClearAllFilters() {
    this.selectedFilter = ''
    const FilterType = this.filtersPayload?.FilterType;
    const ProspectSearch = this.filtersPayload?.ProspectSearch || null;
    const DateType = this.filtersPayload?.DateType || 0;
    const FromDate = this.filtersPayload?.FromDate;
    const ToDate = this.filtersPayload?.ToDate;
    const CustomFilterId = this.filtersPayload?.CustomFilterId;
    const CustomFilterBaseIds = this.filtersPayload?.CustomFilterBaseIds;
    const preservedShowLeadCommunicationCount = this.filtersPayload?.showCommunicationCount;
    const preservedShowLeadCountData = this.filtersPayload?.showFilterCount;

    this.filtersPayload = dmInitialFilterState.filtersPayload;
    this.filtersPayload.showCommunicationCount = preservedShowLeadCommunicationCount;
    this.filtersPayload.showFilterCount = preservedShowLeadCountData;

    this.filtersPayload = {
      ...this.filtersPayload,
      ProspectSearch,
      FilterType,
      DateType,
      FromDate,
      ToDate,
      PageNumber: 1,
      PageSize: this.pageSize,
      CustomFilterId,
      CustomFilterBaseIds,
      showCommunicationCount: preservedShowLeadCommunicationCount,
      showFilterCount: preservedShowLeadCountData,
    };
    this.filterFunction();
  }

  onInView(isVisible: boolean) {
    if (isVisible && this.cardData.length < this.dataTotalCount) {
      this.cardDataPageNumber += 1;
      this.loadMore();
    }
  }

  loadMore() {
    this.currentPageNumber = this.cardDataPageNumber;
    this.currOffset = this.currentPageNumber - 1;

    {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: this.cardDataPageNumber,
      };
    }
    this.filterFunctionCard(false);
  }

  filterFunctionCard(shouldCleanCardData: boolean = true): void {
    if (this.cardData.length !== this.dataTotalCount) {
      if (shouldCleanCardData) this.cleanCardsData();
      const apiPayload = { ...this.filtersPayload };
      if (this.canViewAllData) {
        (apiPayload as any).CanAccessAllProspects = true;
      }
      this._store.dispatch(new UpdateDataFilterPayload(apiPayload));
      this._store.dispatch(new FetchAllData(apiPayload));
    }
  }

  cleanCardsData() {
    this.cardData = [];
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Name',
        field: 'Name',
        hide: false,
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned cursor-pointer text-normal fw-600',
        wrapText: true,
        autoHeight: true,
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => [params?.data?.name],
        cellRenderer: (params: any) => {
          return `<span class="text-truncate-1 break-all"> ${[
            params?.value?.[0],
          ]}</span>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        width: 160,
        valueGetter: (params: any) => [
          params?.data?.status?.displayName,
          params?.data?.status?.color,
        ],
        cellRenderer: (params: any) => {
          return `<span class="status-label-badge" style="color:${params?.value?.[1] || '#4B4B4B'
            }; background-color: ${this.hexToRgba(
              params?.value?.[1] || '#4B4B4B',
              0.08
            )};">
          <span class="dot dot-xs mr-6" style="background-color:${params?.value?.[1] || '#4B4B4B'
            };"></span> <span class="text-nowrap text-truncate-1  break-all">${params?.value?.[0]
            }</span>
          </span>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Scheduled Date',
        field: 'Scheduled Date',
        width: 160,
        valueGetter: (params: any) => [
          params?.data?.scheduleDate
            ? getTimeZoneDate(
              params?.data?.scheduleDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]} </p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
        hide: true,
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        cellClass: 'text-normal fw-600 cursor-pointer',
        wrapText: true,
        autoHeight: true,
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.assignTo,
            this.activeAndInactiveUsers,
            true
          ) || '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]} </p>`;
        },
      },
      {
        headerName: 'Assigned From',
        field: 'Assigned From',
        cellClass: 'text-normal fw-600 cursor-pointer',
        wrapText: true,
        autoHeight: true,
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params?.data?.assignedFrom,
            this.activeAndInactiveUsers,
            true
          ) || '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]} </p>`;
        },
        hide: true,
      },
      {
        headerName: 'Primary/Alt No',
        field: 'Primary/Alt No',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.contactNo,
          params?.data?.alternateContactNo,
        ],
        cellRenderer: (params: any) => {
          return `<p class="gap-2 align-center">
              ${params?.value[0]
              ? `<span class="icon ic-call-ring ic-dark ic-xxs"></span> ${params?.value[0]}`
              : ''
            }</p>
              <p class="gap-2 align-center">${params?.value[1]
              ? `<span class="icon ic-Call ic-dark ic-xxs"></span> ${params?.value[1]}`
              : ''
            }</p>`;
        },
        width: 200,
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Source',
        field: 'Source',
        minWidth: 130,
        cellClass: 'text-normal cursor-pointer',
        wrapText: true,
        autoHeight: true,
        valueGetter: (params: any) => [
          params?.data?.enquiry?.prospectSource?.displayName,
          params?.data?.enquiry?.prospectSource?.imageURL,
          params?.data?.enquiry?.subSource,
        ],
        cellRenderer: (params: any) => `
          <p class="text-truncate-1 break-all fw-600">
            ${params?.value?.[1]
            ? `<img src="${this.s3BucketUrl + params?.value?.[1]
            }" alt="" height="15" class="mr-6">`
            : ''
          }
            <span>${params?.value?.[0] || ''}</span>
          </p>
          <p class="text-truncate-1 break-all ml-24">
            ${params?.value?.[2] || ''}
          </p>`,
      },
      {
        headerName: 'Date of Birth',
        field: 'DateOfBirth',
        minWidth: 130,
        cellClass: 'text-normal cursor-pointer',
        hide: true,
        wrapText: true,
        autoHeight: true,
        valueGetter: (params: any) => [
          params?.data?.dateOfBirth,
        ],
        cellRenderer: (params: any) => {
          const dateOfBirth = params.value[0];
          const formattedDateOfBirth = getTimeZoneDate(dateOfBirth, '00:00:00', 'dayMonthYear');
          return `<p>${dateOfBirth ? formattedDateOfBirth : ''}</p>`;
        },
      },
      {
        headerName: 'Gender',
        field: 'Gender',
        minWidth: 130,
        cellClass: 'text-normal cursor-pointer',
        wrapText: true,
        hide: true,
        autoHeight: true,
        valueGetter: (params: any) => [
          params?.data?.gender,
        ],
        cellRenderer: (params: any) => {
          const gender = params.value[0];
          const formattedGender = this.Gender[gender];
          return `<p>${gender ? formattedGender : ''}</p>`;
        },
      },
      {
        headerName: 'Marital Status',
        field: 'MaritalStatus',
        minWidth: 130,
        hide: true,
        cellClass: 'text-normal cursor-pointer',
        wrapText: true,
        autoHeight: true,
        valueGetter: (params: any) => [
          params?.data?.maritalStatus,
        ],
        cellRenderer: (params: any) => {
          const maritalStatus = params.value[0];
          const formattedMaritalStatus = this.MaritalStatusType[maritalStatus];
          return `<p>${maritalStatus ? formattedMaritalStatus : ''}</p>`;
        },
      },
      {
        headerName: 'Email',
        field: 'Email',
        hide: true,
        valueGetter: (params: any) => [params?.data?.email],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Referral Details',
        field: 'Referral Details',
        hide: true,
        valueGetter: (params: any) => [
          params.data.referralName,
          params.data.referralContactNo,
          params.data.referralEmail,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : ''
            }</p>
            <p class="text-truncate-1 break-all text-sm">${params.value[1] ? params.value[1] : ''
            }</p>
            <p class="text-truncate-1 break-all text-sm">${params.value[2] ? params.value[2] : ''
            }</p>
            `;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Landline Number',
        field: 'Landline Number',
        hide: true,
        valueGetter: (params: any) => [params.data.landLine],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Enquired Location',
        field: 'Enquired Location',
        hide: true,
        valueGetter: (params: any) =>
          params?.data?.enquiry?.addresses
            ?.map((address: any) => getLocationDetailsByObj(address))
            .join('; '),
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all" title='${params?.value}'>${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Built-up Area',
        field: 'Built-up Area',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.builtUpArea,
          params.data?.enquiry?.builtUpAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
            }</p>`;
        },
      },
      {
        headerName: 'Saleable Area',
        field: 'Saleable Area',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.saleableArea,
          params.data?.enquiry?.saleableAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
            }</p>`;
        },
      },
      {
        headerName: 'Enquired City',
        field: 'Enquired City',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.addresses
            ?.map((address: any) => address.city)
            .join(', '),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      // {
      //   headerName: 'Enquired Zone',
      //   field: 'Enquired Zone',
      //   hide: true,
      //   valueGetter: (params: any) => [params.data?.enquiry?.zone],
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value ? params.value : ''}</p>`;
      //   },
      //   cellClass: 'cursor-pointer',
      //   sortable: true,
      //   unSortIcon: true,
      //   colId: 'EnquiryZone',
      //   comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
      //     if (isInverted) {
      //       return nodeB.rowIndex - nodeA.rowIndex;
      //     }
      //     return nodeA.rowIndex - nodeB.rowIndex;
      //   },
      // },
      {
        headerName: 'Enquired State',
        field: 'Enquired State',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.addresses
            ?.map((address: any) => address.state)
            .join(', '),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Enquired Country',
        field: 'Enquired Country',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.addresses
            ?.map((address: any) => address.country)
            .join(', '),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'State',
        field: 'State',
        hide: true,
        valueGetter: (params: any) => {
          const stateArray = params.data?.enquiry?.addresses
            ?.map((address: any) => address.state)
            .filter((state: string) => state) || [];
          return [stateArray.join(', ')];
        },
        cellRenderer: (params: any) => {
          const fullState = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullState}">${fullState}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Country',
        field: 'Country',
        hide: true,
        valueGetter: (params: any) => {
          const countryArray = params.data?.enquiry?.addresses
            ?.map((address: any) => address.country)
            .filter((country: string) => country) || [];
          return [countryArray.join(', ')];
        },
        cellRenderer: (params: any) => {
          const fullCountry = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCountry}">${fullCountry}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Min. Budget',
        field: 'Min. Budget',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          params.data.enquiry?.lowerBudget || '',
          params.data.enquiry?.lowerBudget
            ? params.data?.enquiry?.currency || this.defaultCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },
      {
        headerName: 'Max. Budget',
        field: 'Max. Budget',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          params.data.enquiry?.upperBudget || '',
          params.data.enquiry?.upperBudget
            ? params.data?.enquiry?.currency || this.defaultCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },

      // {
      //   headerName: 'Budget',
      //   field: 'Budget',
      //   minWidth: 200,
      //   hide: true,
      //   valueGetter: (params: any) => {
      //     const lowerBudget = params.data.enquiry?.lowerBudget;
      //     const upperBudget = params.data.enquiry?.upperBudget;
      //     const currency = params.data?.enquiry?.currency
      //       ? params.data?.enquiry?.currency
      //       : this.defaultCurrency;
      //     const lowerBudgetFormatted = lowerBudget
      //       ? `Min. Budget: ${formatBudget(lowerBudget, currency)}`
      //       : '';
      //     const upperBudgetFormatted = upperBudget
      //       ? `Max. Budget: ${formatBudget(upperBudget, currency)}`
      //       : '';
      //     return [lowerBudgetFormatted, upperBudgetFormatted];
      //   },
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value[0]}</p>
      //       <p>${params.value[1]}</p>`;
      //   },
      //   cellClass: 'cursor-pointer',
      // },
      {
        headerName: 'Carpet Area',
        field: 'Carpet Area',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.enquiry?.carpetArea,
          params.data?.enquiry?.carpetAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params?.value?.[0] ? params?.value?.[0] : ''} ${params?.value?.[1] ? params?.value?.[1] : ''
            }</p>`;
        },
      },
      {
        headerName: 'Enquired For',
        field: 'Enquired For',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params.data.enquiry?.enquiryTypes?.map((enquiry: any) => {
            return EnquiryType[enquiry] == 'None' ? '' : EnquiryType[enquiry];
          }) || '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params?.value?.[0]}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Property Details',
        field: 'Property Details',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          const displayName =
            params.data.enquiry?.propertyTypes?.[0]?.displayName || '';
          const childType =
            params.data?.enquiry?.propertyTypes?.map((item: any) => item?.childType?.displayName) || '';
          return [displayName, childType];
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''
            } ${params.value[1]} </p>`;
        },
      },
      {
        headerName: 'Projects',
        field: 'Projects',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.projects?.map((project: any) => project?.name),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params?.value?.[0] ? params?.value?.[0] : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'Projects',
      },
      {
        headerName: 'Property',
        field: 'Property',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.properties?.map((property: any) => property?.title),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params?.value?.[0] ? params?.value?.[0] : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'Properties',
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.agencies?.map((agency: any) => agency.name),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params?.value?.[0] ? params?.value?.[0] : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Channel Partner Name',
        field: 'Channel Partner Name',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.channelPartners?.map((cp: any) => cp.firmName),
        ],
        cellRenderer: (params: any) => {
          const value = params?.value?.[0] ? params?.value?.[0] : '';
          return `<p class="text-truncate-1 break-all" title="${value}">${value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Serial Number',
        field: 'Serial Number',
        hide: true,
        minWidth: 125,
        valueGetter: (params: any) => [params.data.serialNumber || '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">
                  ${params.value[0] ? params.value[0] : '--'}</p>`;
        },
      },
      {
        headerName: 'Sourcing Manager',
        field: 'Sourcing Manager',
        hide: true,
        minWidth: 195,
        valueGetter: (params: any) =>
          getAssignedToDetails(
            params.data.sourcingManager,
            this.allUsers,
            true
          ) || '',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'SourcingManager',
      },
      {
        headerName: 'Closing Manager',
        field: 'Closing Manager',
        hide: true,
        minWidth: 195,
        valueGetter: (params: any) =>
          getAssignedToDetails(
            params.data.closingManager,
            this.allUsers,
            true
          ) || '',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'ClosingManager',
      },

      {
        headerName: 'Profession',
        field: 'Profession',
        hide: true,
        minWidth: 100,
        valueGetter: (params: any) => [params.data.profession],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value[0] ? Profession[params.value[0]] : ''}</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'Profession',
      },
      {
        headerName: 'Customer Address',
        field: 'Customer Address',
        hide: true,
        minWidth: 195,
        valueGetter: (params: any) => [
          getLocationDetailsByObj(params.data?.addressDto),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">${params.value}</p>`;
        },
        cellClass: 'cursor-pointer',
        colId: 'CustomerAddress',
      },
      {
        headerName: 'Campaign Name',
        field: 'Campaign Name',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.campaigns?.length
            ? params?.data?.campaigns?.map((campaigns: any) => campaigns.name)
            : '--',
        ],
        cellRenderer: (params: any) => {
          const value = params?.value?.[0] ? params?.value?.[0] : '--';
          return `<p class="text-truncate-1 break-all" title="${value}">${value}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Company Name',
        field: 'Company Name',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [params.data.companyName],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">
            ${params.value[0] ? params.value[0] : ''}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Notes',
        field: 'Notes',
        hide: true,
        minWidth: 150,
        valueGetter: (params: any) => [params?.data?.notes],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">
            ${params?.value ? params?.value : ''}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Created',
        field: 'Created',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params?.data?.createdBy,
            this.activeAndInactiveUsers,
            true
          ) || '',
          params?.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params?.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params?.value?.[0]}</p>
            <p>${params?.value?.[1]}</p>
                <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Modified',
        field: 'Modified',
        minWidth: 200,
        hide: true,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params?.data?.lastModifiedBy,
            this.activeAndInactiveUsers,
            true
          ) || '',
          params?.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params?.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params?.value?.[0]}</p>
            <p>${params?.value?.[1]}</p>
                <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Possession Needed By',
        field: 'Possession Needed By',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.possesionDate
            ? getTimeZoneDate(
              params?.data?.possesionDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params?.value?.[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Possession Type',
        field: 'Possession Type',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.enquiry?.possesionType
            ? PossessionType[params?.data?.enquiry?.possesionType]
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params?.value?.[0]}</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Deleted Date',
        field: 'Deleted Date',
        hide: true,
        valueGetter: (params: any) => [
          params?.data?.archivedOn
            ? getTimeZoneDate(
              params?.data?.archivedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params?.value?.[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
      {
        headerName: 'Purpose',
        field: 'Purpose',
        hide: true,
        valueGetter: (params: any) => [params.data?.enquiry?.purpose ? PurposeType[params.data?.enquiry?.purpose] : '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : '--'
            }</p>`;
        },
        cellClass: 'cursor-pointer',
      },
    ];
    if (!this.globalSettingsData?.isCustomLeadFormEnabled) {
      this.gridOptions.columnDefs.push(
        {
          headerName: 'BHK',
          field: 'BHK',
          minWidth: 180,
          hide: true,
          valueGetter: (params: any) => [
            params?.data?.enquiry?.bhKs ? params?.data?.enquiry?.bhKs
              ?.map((bhk: any) => getBHKDisplayString(bhk))
              ?.join(', ') : '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
              params.value[0] &&
              params.value[0]?.length > 0
              ? params.value[0]
              : '--'
              } </p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'BHK Types',
          field: 'BHK Types',
          minWidth: 180,
          hide: true,
          valueGetter: (params: any) => [
            params.data.enquiry?.bhkTypes
              ?.map((type: any) => BHKType[type])
              ?.join(', ') || '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
          cellClass: 'cursor-pointer',
        },
      );
    }
    if (this.globalSettingsData?.isCustomLeadFormEnabled) {
      this.gridOptions.columnDefs.push(
        {
          headerName: 'Enquired Sub-Community',
          field: 'Enquired Sub-Community',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.subCommunity)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Enquired Community',
          field: 'Enquired Community',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.community)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Enquired Tower Name',
          field: 'Enquired Tower Name',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.towerName)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Furnish Status',
          field: 'Furnish Status',
          hide: true,
          valueGetter: (params: any) => [
            params.data.enquiry?.furnished
              ? FurnishStatus[params.data.enquiry?.furnished]
              : '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Preferred Floors',
          field: 'Preferred Floors',
          hide: true,
          valueGetter: (params: any) => [params.data.enquiry?.floors],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
              params.value[0] &&
              params.value[0].length > 0
              ? params.value[0]
              : '--'
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Beds',
          field: 'Beds',
          hide: true,
          valueGetter: (params: any) => params.data.enquiry?.beds || [],
          cellRenderer: (params: any) => {
            const value = Array.isArray(params.value)
              ? params.value.map((bed: any) =>
                bed === 0 || bed === '0' ? 'Studio' : bed
              )
              : [];

            return `<p class="text-truncate-1 break-all">${value.length > 0 ? value.join(', ') : '--'
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Baths',
          field: 'Baths',
          hide: true,
          valueGetter: (params: any) => [params.data.enquiry?.baths],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
              params.value[0] &&
              params.value[0].length > 0
              ? params.value[0]
              : '--'
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Offering Type',
          field: 'Offering Type',
          hide: true,
          valueGetter: (params: any) => [
            params.data.enquiry?.offerType
              ? OfferType[params.data.enquiry?.offerType]
              : '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Property Area',
          field: 'Property Area',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.propertyArea,
            params.data?.enquiry?.propertyAreaUnit,
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
              ${params?.value?.[0] ? params?.value?.[0] : ''} ${params?.value?.[1] ? params?.value?.[1] : ''
              }</p>`;
          },
        },
        {
          headerName: 'Net Area',
          field: 'Net Area',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.netArea,
            params.data?.enquiry?.netAreaUnit,
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
              ${params?.value?.[0] ? params?.value?.[0] : ''} ${params?.value?.[1] ? params?.value?.[1] : ''
              }</p>`;
          },
        },
        {
          headerName: 'Unit Number/Name',
          field: 'Unit Number/Name',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.unitName
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : ''
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Cluster Name',
          field: 'Cluster Name',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.enquiry?.clusterName || '--'
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : '--'
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
        {
          headerName: 'Nationality',
          field: 'Nationality',
          hide: true,
          valueGetter: (params: any) => [
            params.data?.nationality || '--'
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : '--'
              }</p>`;
          },
          cellClass: 'cursor-pointer',
        },
      );
    }
    if (this.filtersPayload?.ProspectVisiblity === 0 || this.filtersPayload?.ProspectVisiblity === 1 || this.filtersPayload?.ProspectVisiblity === 2) {
      if (this.canBulkConvertToLead || this.canBulkUpdateStatus || this.canBulkReassign || this.canBulkWhatsapp || this.canBulkEmail || this.canBulkDelete) {
        this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left',
        })
      }
    } else if (this.filtersPayload?.ProspectVisiblity === 3) {
      if (this.canBulkDelete || this.canBulkWhatsapp || this.canBulkEmail) {
        this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left',
        })
      }
    } else if (this.filtersPayload?.ProspectVisiblity === 4) {
      if (this.canBulkDelete || this.canBulkRestore) {
        this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left',
        })
      }
    } else if (this.filtersPayload?.ProspectVisiblity === 5) {
      if (this.canBulkReassign || this.canBulkDelete) {
        this.gridOptions.columnDefs.unshift({
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left',
        })
      }
    }
    this.gridOptions.columnDefs.push({
      headerName: 'Actions',
      field: 'Actions',
      minWidth: 260,
      maxWidth: 260,
      menuTabs: [],
      filter: false,
      suppressMovable: true,
      lockPosition: 'right',
      cellRenderer: DataActionsComponent,
      cellRendererParams: () => ({
        showCommunicationCount: this.showCommunicationCount
      })
    });

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
  }

  /**
   * Function which prepares data for manage columns dropdown
   * and displays only the selected columns
   * it also updates the column state in local storage
   * @param params Grid ready event
   */
  toggleColumns(params: any): void {
    if (this.columnMovedListener) {
      this.gridApi.removeEventListener('columnMoved', this.columnMovedListener);
    }
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.getColDef()?.headerName,
        value: column,
      };
    });
    const sliceStartIndex = this.columns?.[0]?.label === undefined ? 2 : 1;

    this.columns = this.columns
      .slice(sliceStartIndex, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));

    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myDataColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('manage-data-columns')?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
    this.columnMovedListener = () => {
      const updatedColumns = this.gridColumnApi.getAllDisplayedColumns().map((column: any) => ({
        label: column.getColDef().headerName,
        value: column,
      }));

      this.onColumnsSelected(updatedColumns);
    };

    this.gridApi.addEventListener('columnMoved', this.columnMovedListener);
  }

  /**
   * Function to add or remove a column from the grid
   * when selected or deselected from manage column
   * @param columns Array of selected column objects
   */
  onColumnsSelected(columns: any): void {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-data-columns', colData?.toString());
    const cols = columns?.map((col: any) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    if (columnState && columnState[0]) columnState[0].pinned = 'left';
    if (columnState && columnState[1]) columnState[1].pinned = 'left';
    localStorage.setItem('myDataColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
    this.trackingService.trackFeature(`Web.Data.Button.DefaultClick.Click`);
  }

  filterFunction(): void {
    const apiPayload = { ...this.filtersPayload };
    if (this.canViewAllData) {
      (apiPayload as any).CanAccessAllProspects = true;
    }

    this._store.dispatch(new ClearData());
    this._store.dispatch(new FetchAllData(apiPayload)); // Fetch all data

    combineLatest([
      this._store.select(getDataTopFilters).pipe(takeUntil(this.stopper)),
      this._store
        .select(getDataCustomStatusFilter)
        .pipe(takeUntil(this.stopper)),
      this._store
        .select(getIsDataCustomStatusFilterLoading)
        .pipe(takeUntil(this.stopper)),
      this._store
        .select(getIsDataTopFiltersLoading)
        .pipe(takeUntil(this.stopper)),
    ])
      .pipe(
        filter(
          ([
            topLevelFilter,
            firstLevelFilter,
            isLoadingTop,
            isLoadingOthers,
          ]) => {
            return (
              !isLoadingTop &&
              !isLoadingOthers &&
              topLevelFilter &&
              firstLevelFilter &&
              firstLevelFilter.length > 0
            );
          }
        ),
        take(1)
      )
      .subscribe(([topLevelFilter, firstLevelFilter]) => {
        this.topFilters = topLevelFilter?.dataTopLevelFilters?.filter(
          (fil: any) => !fil?.id
        );
        this.isTopLevelFiltersLoading = !topLevelFilter?.isTopFiltersLoadedOnce;
        this.firstLevelFilter = firstLevelFilter;
        this.activeDataId =
          this.firstLevelFilter?.find((fil: any) => fil?.isDefault === true)
            ?.id ??
          this.firstLevelFilter?.find((fil: any) => fil?.name === 'Active Data')
            ?.id;

        this.secondLevelFilterList = this.firstLevelFilter?.filter(
          (fil: any) => {
            if (
              Array.isArray(this.filtersPayload?.CustomFilterBaseIds) &&
              this.filtersPayload?.CustomFilterBaseIds?.length > 1
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds[1];
            } else if (
              Array.isArray(this.filtersPayload?.CustomFilterBaseIds) &&
              this.filtersPayload?.CustomFilterBaseIds?.length === 1
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds[0];
            } else if (
              typeof this.filtersPayload?.CustomFilterBaseIds === 'string'
            ) {
              return fil?.id === this.filtersPayload?.CustomFilterBaseIds;
            } else {
              return fil?.id === this.activeDataId;
            }
          }
        )[0]?.childType;

        this.filtersPayload = {
          ...this.filtersPayload,
          ...(this.activeDataId && !this.filtersPayload.CustomFilterId
            ? { CustomFilterId: this.activeDataId }
            : {}),
          ...(this.activeDataId && !this.filtersPayload.CustomFilterBaseIds
            ? { CustomFilterBaseIds: this.activeDataId }
            : {}),
        };
        const apiPayload = { ...this.filtersPayload, Currency: this.defaultCurrency };
        if (this.canViewAllData) {
          (apiPayload as any).CanAccessAllProspects = true;
        }
        this._store.dispatch(new UpdateDataFilterPayload(apiPayload));
        // this.isLoadingTopFiltersAllAndMyData = false;
        // this.isLoadingTopFiltersOthers = false;
      });
  }

  onCellClicked(event: CellClickedEvent) {
    const headerName = event.colDef.headerName;
    if (headerName === 'Actions') {
      return;
    }
    this.router.navigate([`data/data-preview/${event?.data?.id}`]);
    this.trackingService.trackFeature('Web.Data.Grid.DataPreview.Click');
  }

  openPreview(data: any): void {
    this.router.navigate([`data/data-preview/${data?.id}`]);
  }

  onDataFilterChange(filter: any) {
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: 1,
      FilterType: DataFilterType[filter as keyof typeof DataFilterType],
    };
    this.currOffset = 0;
    this.manageDataCardComponent.cleanCardsData();
    this.filterFunction();
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.currentPageNumber = e + 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: e + 1,
      PageSize: this.pageSize,
    };
    this.trackingService.trackFeature(`Web.Data.Options.${this.pageSize}.Click`);
    this.filterFunction();
  }

  dateChange() {
    if (this.dateType && this.filterDate?.[0]) {
      this.filtersPayload = {
        ...this.filtersPayload,
        DateType: DataDateType[this.dateType as keyof typeof DataDateType],
        FromDate: setTimeZoneDate(
          this.filterDate?.[0],
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        ToDate: setTimeZoneDate(
          this.filterDate?.[1],
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      };
      if (this.dateType) {
        this.trackingService.trackFeature(
          `Web.Data.Options.${this.dateType.replace(/\s+/g, '')}.Click`
        );
      } else if (this.filterDate?.[0]) {
        this.trackingService.trackFeature(
          `Web.Data.Range.CalenderRange.Click`
        );
      }
      this.filterFunction();
    }
  }

  onResetDateFilter() {
    this.filtersPayload = {
      ...this.filtersPayload,
      DateType: 0,
      FromDate: null,
      ToDate: null,
    };
    this.filterDate = [null, null];
    this.filterFunction();
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.pageSize,
      PageNumber: 1,
    };
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.currOffset = 0;
    this.trackingService.trackFeature(`Web.Data.Options.${this.pageSize}.Click`)
    this.filterFunction();
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
      initialState: {
        topFilters: this.topFilters,
        statusList: this.statusList,
        sourceList: this.sourceList,
      },
    };
    const modalRef = this.modalService.show(
      ManageDataAdvanceFiltersComponent,
      initialState
    );
    // if (!this.cleanCards$) {
    //   this.cleanCards$ = modalRef.content.onFilterApplied?.subscribe(() => {
    //     this.manageDataCardComponent.cleanCardsData();
    //   });
    // }
    this.trackingService.trackFeature('Web.Data.Page.AdvanceFilters.Click');
  }

  isSelectedFilter(filter: any): boolean {
    return this.selectedSearchFilters?.some((f: any) => {
      return (typeof f === 'string' ? f : f?.propertyName) === filter?.propertyName;
    });
  }

  getDisplayedFilters() {
    const selectedProps = [
      ...(this.selectedSearchFilters || []),
      ...(this.recentSearches || [])
    ].map((f: any) => typeof f === 'string' ? f : f?.propertyName);

    return this.allSearchFilters?.filter(
      (filter: any) => !selectedProps.includes(filter?.propertyName)
    )?.slice(0, 15);
  }

  toggleSearchFilter(filter: any) {
    const filterKey = typeof filter === 'string' ? filter : filter?.propertyName;

    const selectedIndex = this.selectedSearchFilters?.findIndex(
      (item: any) => (typeof item === 'string' ? item : item?.propertyName) === filterKey
    );

    const recentIndex = this.recentSearches?.findIndex(
      (item: any) => item?.propertyName === filterKey
    );

    if (selectedIndex > -1) {
      this.selectedSearchFilters
        .splice(selectedIndex, 1);
      return;
    }

    if (recentIndex > -1) {
      const updatedRecentSearches = [...this.recentSearches];
      updatedRecentSearches.splice(recentIndex, 1);
      this.selectedSearchFilters = [...updatedRecentSearches];
      this.recentSearches = [];
      return;
    }

    if (this.recentSearches?.length > 0 && this.selectedSearchFilters?.length === 0) {
      this.selectedSearchFilters = [...this.recentSearches];
      this.recentSearches = [];
    }

    const totalFilters = this.selectedSearchFilters?.length;
    if (totalFilters < 5) {
      const matchingFilter = this.allSearchFilters?.find(f => f.propertyName === filterKey);
      if (matchingFilter) {
        const updatedSelectedFilters = [...this.selectedSearchFilters, matchingFilter];
        this.selectedSearchFilters = updatedSelectedFilters;
      }
    }
  }

  getSelectedFilters() {
    if (this.selectedSearchFilters?.length > 0) {
      const selectedSearches = this.selectedSearchFilters?.map((item: any) => item?.propertyName);
      return this.allSearchFilters?.filter(filter =>
        selectedSearches?.includes(filter?.propertyName)
      ) || [];
    }
    if (this.recentSearches?.length > 0) {
      const recentSearches = this.recentSearches?.map((item: any) => item?.propertyName);
      return this.allSearchFilters?.filter(filter =>
        recentSearches.includes(filter?.propertyName)
      ) || [];
    }
    return [];
  }

  toggleSearchDropdown() {
    this.showSearchDropdown = !this.showSearchDropdown;
    if (this.showSearchDropdown) {
      if (this.selectedSearchFilters.length === 0) {
        this._store.select(getRecentSearch)
          .pipe(
            takeUntil(this.stopper),
            skipWhile((data: any) => !data?.length)
          )
          .subscribe((recentSearches: string[]) => {
            if (recentSearches && recentSearches?.length > 0) {
              this.recentSearches = recentSearches;
            }
          });
      }
    }
  }

  onSearch() {
    if (this.searchTerm) {
      if (this.recentSearches?.length > 0 && this.selectedSearchFilters?.length === 0) {
        this.selectedSearchFilters = [...this.recentSearches];
        this.recentSearches = [];
      }

      const searchPayload: any = {
        ProspectSearch: this.searchTerm,
        PageNumber: 1,
      };

      const displayedFilters = this.getSelectedFilters();
      if (displayedFilters && displayedFilters?.length > 0) {
        const propertyNames = displayedFilters.map(filter => filter.propertyName);
        searchPayload.PropertyToSearch = propertyNames;
        this.filtersPayload = {
          ...this.filtersPayload,
          PropertyToSearch: propertyNames
        };
        const recentSearched = this.recentSearches?.map((item: any) => item?.propertyName);
        if (!propertyNames?.includes(recentSearched)) {
          this._store.dispatch(new UpdateUserSearch({
            module: 1,
            searchResults: propertyNames
          }));
        }
      }
      if (this.getSelectedFilters()?.length === 0) {
        this._store.dispatch(new UpdateUserSearch({
          module: 1,
          searchResults: []
        }));
      }

      this.filtersPayload = {
        ...this.filtersPayload,
        ...searchPayload
      };
      this.filterFunction();
      this.selectedSearchFilters = [];
      this.showSearchDropdown = false;
    }
    this.trackingService.trackFeature('Web.Data.DataEntry.Search.DataEntry');
  }

  isEmptyInput() {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  onResize(): void {
    this.ngZone.run(() => {
      if (window.innerWidth < 480 && !this.widthBelowThreshold) {
        this.widthBelowThreshold = true;
        this.cardData = [];
        this.cardDataPageNumber = 1;
        this.filtersPayload.PageNumber = this.cardDataPageNumber;
        this.loadMore();
      } else if (window.innerWidth >= 480) {
        this.widthBelowThreshold = false;
      }
    });
  }

  openDataBulkUploadQR() {
    if (this.selectedAddDataOption === 'bulkUpload') {
      this.router.navigate(['data/bulk-upload']);
      this.trackingService.trackFeature('Web.Data.Options.BulkUpload.Click');
    }
    this.selectedAddDataOption = '';
  }

  openDMTracker() {
    let initialState: any = {
      fieldType: 'data',
    };
    this._store.dispatch(new FetchDataExcelUploadedList(1, 10));
    this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState,
    });
  }

  openDataTracker() {
    let initialState: any = null;
    if (this.selectedDataTrackerOption === 'bulkUpload') {
      this.trackingService.trackFeature('Web.Data.Options.BulkUploadTracker.Click')
      this.openDMTracker();
    } else if (this.selectedDataTrackerOption === 'export') {
      this.trackingService.trackFeature('Web.Data.Options.ExportTracker.Click')
      this._store.dispatch(new FetchDataExportStatus(1, 10));
      initialState = {
        isDataManagement: true,
      };
      this.modalService.show(ExportLeadsTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedDataTrackerOption === 'bulk') {
      this.trackingService.trackFeature('Web.Data.Options.BulkOperation.Click')
      this._store.dispatch(new FetchBulkOperation(1, 10, 'prospect'));
      this.modalService.show(BulkOperationTrackerComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState: {
          moduleType: 'prospect',
        },
      });
    }
    this.selectedDataTrackerOption = '';
  }

  navigateToAddData() {
    this.router.navigate(['data/add-data']);
    this.trackingService.trackFeature(`Web.Data.Button.AddData.Click`);
  }

  activeSecondLevelFilter(filter: any) {
    return this.filtersPayload.CustomFilterBaseIds?.[0] === filter?.id;
  }

  openSavedFilter(event: any) {
    this.isSavedFilterOpen = !this.isSavedFilterOpen;
  }

  onSaveFilter(addFile: TemplateRef<any>) {
    if (!this.isFilterUpdated) {
      this.FileForm.patchValue({
        name: null,
      });
    }
    let initialState: any = {
      class: 'modal-350 top-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(addFile, initialState);

    this.modalRef.onHidden.subscribe(() => {
      this.FileForm.get('name').markAsUntouched();
      this.FileForm.get('name').setErrors(null);
    });
  }

  onSubmit(): void {
    if (!this.FileForm.valid || this.doesFileNameExist) {
      validateAllFormFields(this.FileForm);
      return;
    }

    let enumPayload: any = {
      ...this.filtersPayload
    }

    Object.entries(enumPayload).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          enumPayload[key] = getIndexes(key, value);
        } else {
          enumPayload[key] = value;
        }
      }
    });

    const payload = {
      name: this.FileForm.value.name,
      module: 'prospects',
      filterCriteria: JSON.stringify(enumPayload),
    };

    if (this.isFilterUpdated) {
      this.updateFilter(payload);
    } else {
      this.saveFilter(payload);

    }

    this.modalService.hide();
  }

  saveFilter(payload: any): void {
    this._store.dispatch(new SaveFilter(payload));
    this.selectedFilter = JSON.stringify(this.filtersPayload || {});
  }

  updateFilter(payload: any): void {
    const updatedPayload = { ...payload, id: this.selectedFilterId };
    this._store.dispatch(new UpdateSavedFilter(updatedPayload));
    this.selectedFilter = JSON.stringify(this.filtersPayload || {});
  }

  handleSelectFilter(updatedFilter: any) {
    let payload: any = {
      ...updatedFilter?.filterCriteria
    }
    Object.entries(payload).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          payload[key] = getIndexes(key, value);
        } else {
          payload[key] = value;
        }
      }
    });
    this.filtersPayload = { ...payload }
    this.selectedFilterName = updatedFilter?.name
    this.selectedFilter = JSON.stringify(updatedFilter?.filterCriteria);
    this.selectedFilterId = updatedFilter?.id;
    this.FileForm.patchValue({
      name: updatedFilter?.name,
    })
    this._store.dispatch(new UpdateDataFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAllData(this.filtersPayload));
    this.closeSavedFilter()
  }

  handleEditFilter(updatedFilter: any) {
    const initialState: any = {
      filterData: updatedFilter,
      topFilters: this.topFilters,
      allUsers: this.activeAndInactiveUsers,
      statusList: this.statusList,
      sourceList: this.sourceList,
    };
    this.selectedFilter = JSON.stringify(updatedFilter?.filterCriteria)
    this.selectedFilterId = updatedFilter?.id;
    this.selectedFilterName = updatedFilter?.name
    this.FileForm.patchValue({
      name: updatedFilter?.name,
    })

    const modalRef = this.modalService.show(
      ManageDataAdvanceFiltersComponent,
      {
        initialState,
        class: 'ip-modal-unset  top-full-modal',
      }
    );
    this.closeSavedFilter()
  }

  closeSavedFilter() {
    this.isSavedFilterOpen = false;
  }

  @HostListener('document:click', ['$event'])
  handleOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    const savedFilterDiv = document.getElementById('saved-filter');
    const searchFilterDiv = document.getElementById('search-dropdown');
    if (savedFilterDiv && savedFilterDiv.contains(target)) {
      return;
    }
    if (searchFilterDiv && searchFilterDiv.contains(target)) {
      return;
    }
    this.isSavedFilterOpen = false;
    this.showSearchDropdown = false;
  }

  trackerFeatures(visibility: any) {
    this.trackingService.trackFeature(`Web.Data.Menu.${visibility.replace(/\s+/g, '')}.Click`)
  }

  trackerFirstLevelFilter(firstLevelFilter: any) {
    this.trackingService.trackFeature(`Web.Data.MenuLevel1.${firstLevelFilter.replace(/\s+/g, '')}.Click`)
  }

  doesFileNameExists() {
    let name = this.FileForm.value.name;

    if (this.isFilterUpdated && this.selectedFilterName === name) {
      this.doesFileNameExist = false;
      return;
    }
    if (!name) {
      this.doesFileNameExist = false;
      return;
    }
    this._store.dispatch(new FilterExist(name, 'prospects'));
    this._store.dispatch(new LoaderHide());
    this._store
      .select(getFilterExist)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.doesFileNameExist = data;
      });
  }

  onshowLeadCountData() {
    this._store.dispatch(new FetchAllData({ ...this.filtersPayload, showFilterCount: true }));
    this.showFilterCount = true
  }


  onshowcommunicationCountData() {
    if (!this.rowData?.length) return;

    const ProspectIds = this.rowData.map((data: any) => data?.id).filter(Boolean);
    const batchSize = 50;

    for (let i = 0; i < ProspectIds.length; i += batchSize) {
      const payload = { ProspectIds: ProspectIds.slice(i, i + batchSize) };
      this._store.dispatch(new UpdateDataFilterPayload({ ...this.filtersPayload, showCommunicationCount: true }));

      this._store.dispatch(
        new FetchDataCommunicationByIds({
          ...payload,
          showCommunicationCount: true
        })
      );
    }

    this.showCommunicationCount = true;
    if (this.gridApi && this.gridApi) {
      this.gridApi.refreshCells({ columns: ['Actions'], force: true });
    }
  }


  ngOnDestroy() {
    if (this.cleanCards$) {
      this.cleanCards$.unsubscribe();
    }
    this._store.dispatch(new UpdateAllDataIsLoading());
    this.stopper.next();
    this.stopper.complete();
    if (this.resizeListener) {
      this.resizeListener();
    }
  }
}
